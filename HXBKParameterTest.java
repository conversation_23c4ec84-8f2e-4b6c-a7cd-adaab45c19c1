import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 模拟HXBK参数处理测试
 * 测试JSONObject.from()和参数删除的实际行为
 */
public class HXBKParameterTest {
    
    public static void main(String[] args) {
        System.out.println("=== HXBK参数处理测试 ===");
        
        // 创建模拟的HXBK请求对象
        MockHXBKCreditApplyRequest request = new MockHXBKCreditApplyRequest();
        
        // 1. 模拟JSONObject.from()的行为
        System.out.println("1. 模拟JSONObject.from()序列化:");
        Map<String, Object> jsonObject = simulateJSONObjectFrom(request);
        
        System.out.println("序列化后的内容:");
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 2. 检查是否包含method和version
        boolean originalHasMethod = jsonObject.containsKey("method");
        boolean originalHasVersion = jsonObject.containsKey("version");
        
        System.out.println("\n2. 检查原始序列化结果:");
        System.out.println("包含method字段: " + originalHasMethod);
        System.out.println("包含version字段: " + originalHasVersion);
        
        // 3. 删除method和version字段
        System.out.println("\n3. 删除method和version字段:");
        jsonObject.remove("method");
        jsonObject.remove("version");
        
        System.out.println("删除后的内容:");
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 4. 模拟putParametersFromObject
        System.out.println("\n4. 模拟AntCloudClientRequest.putParametersFromObject():");
        Map<String, Object> parameters = new HashMap<>(jsonObject);
        
        System.out.println("最终parameters内容:");
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 5. 最终检查
        boolean finalHasMethod = parameters.containsKey("method");
        boolean finalHasVersion = parameters.containsKey("version");
        
        System.out.println("\n=== 最终检查结果 ===");
        System.out.println("parameters包含method字段: " + finalHasMethod);
        System.out.println("parameters包含version字段: " + finalHasVersion);
        
        if (finalHasMethod) {
            System.out.println("parameters中的method值: " + parameters.get("method"));
        }
        if (finalHasVersion) {
            System.out.println("parameters中的version值: " + parameters.get("version"));
        }
        
        // 6. 模拟实际的AntCloudClientRequest行为
        System.out.println("\n=== 模拟AntCloudClientRequest完整流程 ===");
        MockAntCloudClientRequest antRequest = new MockAntCloudClientRequest();
        
        // 设置method和version（对应HXBKRequestService第87-89行）
        antRequest.setMethod(request.getMethod().getName());
        antRequest.setVersion(request.getMethod().getVersion());
        
        System.out.println("设置method和version后:");
        System.out.println("antRequest.getMethod(): " + antRequest.getMethod());
        System.out.println("antRequest.getVersion(): " + antRequest.getVersion());
        
        // 将参数放入（对应第96行）
        antRequest.putParametersFromObject(jsonObject);
        
        Map<String, Object> finalParameters = antRequest.getParameters();
        System.out.println("\n最终antRequest.getParameters():");
        for (Map.Entry<String, Object> entry : finalParameters.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        boolean antRequestHasMethod = finalParameters.containsKey("method");
        boolean antRequestHasVersion = finalParameters.containsKey("version");
        
        System.out.println("\n=== AntCloudClientRequest最终结果 ===");
        System.out.println("antRequest.parameters包含method: " + antRequestHasMethod);
        System.out.println("antRequest.parameters包含version: " + antRequestHasVersion);
        System.out.println("antRequest.getMethod(): " + antRequest.getMethod());
        System.out.println("antRequest.getVersion(): " + antRequest.getVersion());
        
        // 结论
        System.out.println("\n=== 结论 ===");
        if (!antRequestHasMethod && !antRequestHasVersion) {
            System.out.println("✓ 成功：parameters中不包含method和version字段");
            System.out.println("  但antRequest对象本身仍保留method和version属性");
        } else {
            System.out.println("✗ 问题：parameters中仍包含method和/或version字段");
        }
    }
    
    /**
     * 模拟JSONObject.from()的行为
     * 通过反射获取对象的所有属性，但排除@JsonIgnore标注的方法
     */
    private static Map<String, Object> simulateJSONObjectFrom(Object obj) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有public方法
        Method[] methods = obj.getClass().getMethods();
        for (Method method : methods) {
            String methodName = method.getName();
            
            // 只处理getter方法
            if (methodName.startsWith("get") && method.getParameterCount() == 0 
                && !methodName.equals("getClass")) {
                
                try {
                    // 模拟@JsonIgnore检查 - getMethod()方法被@JsonIgnore标注
                    if (methodName.equals("getMethod")) {
                        System.out.println("  跳过@JsonIgnore方法: " + methodName);
                        continue;
                    }
                    
                    Object value = method.invoke(obj);
                    if (value != null) {
                        // 转换方法名为属性名
                        String propertyName = methodName.substring(3);
                        propertyName = propertyName.substring(0, 1).toLowerCase() + propertyName.substring(1);
                        result.put(propertyName, value);
                    }
                } catch (Exception e) {
                    System.out.println("  调用方法失败: " + methodName + " - " + e.getMessage());
                }
            }
        }
        
        return result;
    }
    
    // 模拟HXBK授信申请请求
    static class MockHXBKCreditApplyRequest {
        private String orderNo = "TEST_ORDER_20250729001";
        private String channelType = "1";
        private String customType = "1";
        private String fundCode = "HXBK";
        private String openId = "test_open_id_123";
        private MockHXBKMethod method = MockHXBKMethod.CREDIT_APPLY;
        
        public String getOrderNo() { return orderNo; }
        public String getChannelType() { return channelType; }
        public String getCustomType() { return customType; }
        public String getFundCode() { return fundCode; }
        public String getOpenId() { return openId; }
        
        // 模拟@JsonIgnore标注的方法
        public MockHXBKMethod getMethod() { return method; }
    }
    
    // 模拟HXBK方法枚举
    enum MockHXBKMethod {
        CREDIT_APPLY("riskplus.dubbridge.credit.apply", "1.0");
        
        private final String name;
        private final String version;
        
        MockHXBKMethod(String name, String version) {
            this.name = name;
            this.version = version;
        }
        
        public String getName() { return name; }
        public String getVersion() { return version; }
    }
    
    // 模拟AntCloudClientRequest
    static class MockAntCloudClientRequest {
        private String method;
        private String version;
        private Map<String, Object> parameters = new HashMap<>();
        
        public void setMethod(String method) { this.method = method; }
        public void setVersion(String version) { this.version = version; }
        public String getMethod() { return method; }
        public String getVersion() { return version; }
        
        public void putParametersFromObject(Map<String, Object> object) {
            parameters.putAll(object);
        }
        
        public Map<String, Object> getParameters() { return parameters; }
    }
}
