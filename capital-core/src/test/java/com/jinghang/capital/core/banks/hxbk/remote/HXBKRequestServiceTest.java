package com.jinghang.capital.core.banks.hxbk.remote;

import cn.com.antcloud.api.antcloud.AntCloudClientRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditApplyRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKPersonalInfo;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

import java.util.Map;

/**
 * HXBK请求服务测试类
 * 用于测试删除method和version字段后，antRequest的parameters中是否还包含这些字段
 *
 * @Author: Test
 * @CreateTime: 2025/7/29
 */
public class HXBKRequestServiceTest {

    /**
     * 主方法，用于直接运行测试
     */
    public static void main(String[] args) {
        HXBKRequestServiceTest test = new HXBKRequestServiceTest();
        test.testAntRequestParametersProcessing();
        test.testJSONObjectFromBehavior();
    }

    /**
     * 测试AntCloudClientRequest参数处理逻辑
     * 验证删除method和version字段后，parameters中是否还包含这些字段
     */
    public void testAntRequestParametersProcessing() {
        // 创建测试请求对象
        HXBKCreditApplyRequest request = createTestCreditApplyRequest();
        
        // 模拟HXBKRequestService中的参数处理逻辑
        System.out.println("=== 开始测试AntCloudClientRequest参数处理 ===");

        // 1. 创建AntCloudClientRequest
        AntCloudClientRequest antRequest = new AntCloudClientRequest();

        // 2. 设置method和version（模拟第87-89行的逻辑）
        antRequest.setMethod(request.getMethod().getName());
        antRequest.setVersion(request.getMethod().getVersion());

        System.out.println("设置method和version后:");
        System.out.println("antRequest.getMethod(): " + antRequest.getMethod());
        System.out.println("antRequest.getVersion(): " + antRequest.getVersion());

        // 3. 将请求对象转换为JSON（模拟第92行）
        JSONObject object = JSONObject.from(request);
        System.out.println("转换为JSONObject后的内容: " + JSON.toJSONString(object));

        // 4. 删除method和version字段（模拟第94-95行）
        object.remove("method");
        object.remove("version");
        System.out.println("删除method和version后的JSONObject: " + JSON.toJSONString(object));

        // 5. 将参数放入antRequest（模拟第96行）
        antRequest.putParametersFromObject(object);

        // 6. 检查最终的parameters内容
        Map<String, Object> parameters = antRequest.getParameters();
        System.out.println("=== 最终antRequest.getParameters()内容 ===");
        System.out.println("Parameters: " + JSON.toJSONString(parameters));

        // 7. 检查是否包含method和version字段
        boolean containsMethod = parameters.containsKey("method");
        boolean containsVersion = parameters.containsKey("version");

        System.out.println("=== 检查结果 ===");
        System.out.println("parameters是否包含'method'字段: " + containsMethod);
        System.out.println("parameters是否包含'version'字段: " + containsVersion);

        if (containsMethod) {
            System.out.println("parameters中的method值: " + parameters.get("method"));
        }
        if (containsVersion) {
            System.out.println("parameters中的version值: " + parameters.get("version"));
        }

        // 8. 检查antRequest对象本身的method和version
        System.out.println("=== AntCloudClientRequest对象本身的method和version ===");
        System.out.println("antRequest.getMethod(): " + antRequest.getMethod());
        System.out.println("antRequest.getVersion(): " + antRequest.getVersion());

        // 9. 打印完整的antRequest对象信息
        System.out.println("=== 完整的AntCloudClientRequest对象 ===");
        System.out.println("完整antRequest对象: " + JSON.toJSONString(antRequest));
    }
    

    

    
    /**
     * 创建测试用的授信申请请求对象
     */
    private HXBKCreditApplyRequest createTestCreditApplyRequest() {
        HXBKCreditApplyRequest request = new HXBKCreditApplyRequest();
        request.setOrderNo("TEST_ORDER_20250729001");
        request.setChannelType("1");
        request.setCustomType("1");
        request.setFundCode("HXBK");
        request.setOpenId("test_open_id_123");
        request.setProdNo("PROD001");
        request.setApplicationsAmount("50000");
        request.setApplicationDeadline("12");
        request.setLoanReason("个人消费");
        
        // 创建个人信息
        HXBKPersonalInfo personalInfo = new HXBKPersonalInfo();
        personalInfo.setCustName("张三");
        personalInfo.setIdNo("110101199001011234");
        personalInfo.setMobile("13800138000");
        personalInfo.setGender("1");
        personalInfo.setBirthday("1990-01-01");
        request.setPersonalInfo(personalInfo);
        
        return request;
    }
    
    /**
     * 测试JSONObject.from()方法的行为
     */
    public void testJSONObjectFromBehavior() {
        System.out.println("=== 测试JSONObject.from()方法的行为 ===");

        HXBKCreditApplyRequest request = createTestCreditApplyRequest();

        // 测试JSONObject.from()是否会包含@JsonIgnore标注的method
        JSONObject object = JSONObject.from(request);

        System.out.println("JSONObject.from()结果: " + JSON.toJSONString(object));
        System.out.println("是否包含method字段: " + object.containsKey("method"));
        System.out.println("是否包含version字段: " + object.containsKey("version"));

        // 手动调用getMethod()方法
        HXBKMethod method = request.getMethod();
        System.out.println("手动调用getMethod(): name=" + method.getName() + ", version=" + method.getVersion());
    }
}
