package com.jinghang.capital.core.banks.hxbk.callback.service;

import cn.hutool.core.util.ObjectUtil;
import com.jinghang.capital.core.banks.hxbk.callback.dto.CreditResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.callback.dto.MethodResponse;
import com.jinghang.capital.core.banks.hxbk.callback.dto.LoanApplyResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.callback.dto.RepayResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditQueryRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditQueryResponse;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKResponseCode;
import com.jinghang.capital.core.banks.hxbk.remote.HXBKRequestService;
import com.jinghang.capital.core.banks.hxbk.service.HXBKCreditService;
import com.jinghang.capital.core.banks.hxbk.service.HXBKLoanService;
import com.jinghang.capital.core.banks.hxbk.service.HXBKRepayService;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.HXBKCreditFlow;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.HXBKCreditFlowRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * HXBK回调业务处理服务
 * 根据method分发到不同的业务处理逻辑
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 22:00
 */
@Service
public class HXBKCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKCallbackService.class);

    @Autowired
    private HXBKCreditFlowRepository hxbkCreditFlowRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HXBKCreditService hxbkCreditService;
    @Autowired
    private HXBKLoanService hxbkLoanService;
    @Autowired
    private HXBKRepayService hxbkRepayService;
    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private HXBKRequestService hxbkRequestService;
    @Autowired
    private WarningService warningService;

    /**
     * 处理回调请求
     *
     * @param method     回调方法名
     * @param requestId  请求ID
     * @param bizDataStr 解密后的业务数据JSON字符串
     * @return 业务响应数据
     */
    public MethodResponse processCallback(String method, String requestId, String bizDataStr) throws Exception {
        logger.info("开始处理HXBK回调业务，method: {}, requestId: {}, bizDataStr: {}", method, requestId, bizDataStr);

        if (HXBKMethod.CREDIT_APPLY_RESULT_NOTIFY.getName().equals(method)) {
            // 3.1 授信申请异步通知
            return processCreditResultCallback(requestId, bizDataStr);
        } else if (HXBKMethod.LOAN_RESULT_CALLBACK.getName().equals(method)) {
            // 3.2 用信申请异步通知
            return processLoanResultCallback(requestId, bizDataStr);
        } else if (HXBKMethod.REPAY_RESULT_CALLBACK_V2.getName().equals(method)) {
            // 3.4 还款异步通知V2.0
            return processRepayResultCallback(requestId, bizDataStr);
        } else {
            logger.error("不支持的回调方法: {}", method);
            throw new RuntimeException("不支持的方法: " + method);
        }
    }

    /**
     * 处理授信结果回调
     *
     * @param requestId  请求ID
     * @param bizDataStr 解密后的业务数据JSON字符串
     * @return 响应数据
     */
    private MethodResponse processCreditResultCallback(String requestId, String bizDataStr) throws Exception {
        logger.info("处理授信结果回调，requestId: {}", requestId);

        // 解析业务数据为具体的DTO对象
        CreditResultCallbackRequest callbackRequest = JsonUtil.convertToObject(bizDataStr, CreditResultCallbackRequest.class);
        logger.info("授信结果回调业务数据: {}", JsonUtil.toJsonString(callbackRequest));

        // 校验数据
        String orderNo = callbackRequest.getOrderNo();
        if (StringUtil.isBlank(orderNo)) {
            String msg = "orderNo为空，无法处理回调";
            logger.error(msg);
            return MethodResponse.failure(msg);
        }

        // 查找授信记录
        Credit credit = creditRepository.findCreditById(orderNo);
        if (ObjectUtil.isNull(credit)) {
            String msg = "未找到对应的授信记录，orderNo（credit表的主键id）: " + orderNo;
            logger.error(msg);
            return MethodResponse.failure(msg);
        }

        logger.info("通过orderNo（credit表的主键id）查找到授信记录，orderNo: {}", orderNo);

        // 新增：主动查询验证
        boolean isConsistent = verifyCreditResultConsistency(credit, callbackRequest);
        if (!isConsistent) {
            // 数据不一致，返回失败
            String errorMsg = String.format("授信回调数据不一致，或主动查询异常，creditId: %s, customNo: %s",
                    credit.getId(), callbackRequest.getCustomNo());
            logger.error(errorMsg);
            return MethodResponse.failure("数据不一致，或主动查询异常，处理失败");
        }

        // 处理授信结果
        processCreditResult(credit, callbackRequest);

        logger.info("授信结果回调处理成功，creditId: {}, status: {}", credit.getId(), callbackRequest.getStatus());
        return MethodResponse.success();
    }

    /**
     * 处理放款结果回调
     *
     * @param requestId  请求ID
     * @param bizDataStr 解密后的业务数据JSON字符串
     * @return 响应数据
     */
    private MethodResponse processLoanResultCallback(String requestId, String bizDataStr) throws Exception {
        logger.info("处理放款结果回调，requestId: {}", requestId);

        LoanApplyResultCallbackRequest request = JsonUtil.convertToObject(bizDataStr, LoanApplyResultCallbackRequest.class);
        logger.info("放款结果回调业务数据: {}", bizDataStr);
        hxbkLoanService.doLoanApplyResultCallback(request);

        return MethodResponse.success();
    }

    /**
     * 处理还款结果回调
     *
     * @param requestId  请求ID
     * @param bizDataStr 解密后的业务数据JSON字符串
     * @return 响应数据
     */
    private MethodResponse processRepayResultCallback(String requestId, String bizDataStr) throws Exception {
        logger.info("处理还款结果回调，requestId: {}", requestId);
        RepayResultCallbackRequest request = JsonUtil.convertToObject(bizDataStr, RepayResultCallbackRequest.class);
        hxbkRepayService.doRepayResultCallback(request);
        logger.info("还款结果回调业务数据: {}", bizDataStr);
        return MethodResponse.success();
    }

    /**
     * 处理授信结果
     *
     * @param credit          授信记录
     * @param callbackRequest 回调请求数据
     */
    private void processCreditResult(Credit credit, CreditResultCallbackRequest callbackRequest) {
        logger.info("开始处理授信结果，creditId: {}, status: {}", credit.getId(), callbackRequest.getStatus());

        // 使用HXBKCreditService的统一方法处理授信结果
        hxbkCreditService.processCreditResultCommon(credit, null, callbackRequest,
                "callback");

        logger.info("授信结果处理完成，creditId: {}, status: {}", credit.getId(), callbackRequest.getStatus());
    }

    /**
     * 验证授信回调结果与主动查询结果的一致性
     *
     * @param credit          授信记录
     * @param callbackRequest 回调请求数据
     * @return true-一致，false-不一致
     */
    private boolean verifyCreditResultConsistency(Credit credit,
                                                  CreditResultCallbackRequest callbackRequest) {
        try {
            logger.info("开始验证授信回调数据一致性，creditId: {}", credit.getId());

            // 构建主动查询请求
            HXBKCreditQueryRequest queryRequest = buildCreditQueryRequest(credit);

            // 调用主动查询接口
            logger.info("HXBK授信回调验证查询, 请求参数: {}", JsonUtil.toJsonString(queryRequest));
            HXBKCreditQueryResponse queryResponse = hxbkRequestService.creditQuery(queryRequest);
            logger.info("HXBK授信回调验证查询, 响应参数: {}", JsonUtil.toJsonString(queryResponse));

            if (queryResponse == null || HXBKResponseCode.isNotOk(queryResponse.getResultCode())) {
                logger.warn("主动查询授信结果失败，无法验证一致性，creditId: {}, response: {}",
                        credit.getId(), JsonUtil.toJsonString(queryResponse));
                // 查询失败时阻断流程
                return false;
            }

            // 对比关键字段
            boolean isConsistent = compareResults(callbackRequest, queryResponse, credit.getId());
            logger.info("授信回调数据一致性验证完成，creditId: {}, 结果: {}", credit.getId(), isConsistent);

            // 将主动查询的apply_no赋值给callbackRequest，因为主动查询和回调的apply_no不同，以主查询为准
            callbackRequest.setApplyNo(queryResponse.getApplyNo());

            return isConsistent;

        } catch (Exception e) {
            logger.error("授信结果一致性验证异常，creditId: {}", credit.getId(), e);
            warningService.warn(String.format("HXBK授信回调验证异常，creditId: %s, 异常: %s",
                    credit.getId(), e.getMessage()));
            // 异常时阻断流程
            return false;
        }
    }

    /**
     * 对比回调结果与查询结果
     *
     * @param callbackRequest 回调请求数据
     * @param queryResponse   查询响应数据
     * @param creditId        授信ID
     * @return true-一致，false-不一致
     */
    private boolean compareResults(CreditResultCallbackRequest callbackRequest,
                                   HXBKCreditQueryResponse queryResponse,
                                   String creditId) {
        List<String> differences = new ArrayList<>();

        // 1. 授信状态对比
        if (!Objects.equals(callbackRequest.getStatus(), queryResponse.getStatus())) {
            differences.add(String.format("授信状态不一致: callback=%s, query=%s",
                    callbackRequest.getStatus(), queryResponse.getStatus()));
        }

        // 以下参数仅在授信成功才对比
        if ("0".equals(callbackRequest.getStatus())) {
            // 2. 授信额度对比
            if (!Objects.equals(callbackRequest.getCreditAmt(), queryResponse.getCreditAmt())) {
                differences.add(String.format("授信额度不一致: callback=%s, query=%s",
                        callbackRequest.getCreditAmt(), queryResponse.getCreditAmt()));
            }

            // 3. 客户编号对比
            if (!Objects.equals(callbackRequest.getCustomNo(), queryResponse.getCustomNo())) {
                differences.add(String.format("客户编号不一致: callback=%s, query=%s",
                        callbackRequest.getCustomNo(), queryResponse.getCustomNo()));
            }
        }

        if (!differences.isEmpty()) {
            String errorDetail = String.join("; ", differences);
            logger.error("授信结果不一致详情，creditId: {}, 差异: {}", creditId, errorDetail);
            warningService.warn(String.format("HXBK授信回调数据不一致，creditId: %s, 详情: %s",
                    creditId, errorDetail));
            return false;
        }

        return true;
    }

    /**
     * 构建授信查询请求
     *
     * @param credit 授信记录
     * @return 查询请求对象
     */
    private HXBKCreditQueryRequest buildCreditQueryRequest(Credit credit) {
        HXBKCreditQueryRequest request = new HXBKCreditQueryRequest();
        // 生成新的查询订单号
        request.setOrderNo(IdGenUtil.genReqNo());
        // 使用原始授信申请订单号
        request.setOriginalOrderNo(credit.getId());
        return request;
    }

}
