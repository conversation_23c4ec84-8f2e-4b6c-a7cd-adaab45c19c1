package com.jinghang.capital.core.banks.cybk.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKConfig {

    @Value("${cybk.server.url}")
    private String cybkServerUrl;

    @Value("${cybk.appid}")
    private String appid;

    @Value("${cybk.channel}")
    private String channel;

    @Value("${cybk.channel.id}")
    private String channelId;

    @Value("${cybk.merchant.shortCode}")
    private String shortCode;

    @Value("${cybk.loanType}")
    private String loanType;

    @Value("${cybk.merchant.code}")
    private String merchantCode;

    @Value("${cybk.merchant.shop}")
    private String merchantShop;

    @Value("${cybk.merchant.terminal}")
    private String merchantTerminal;

    /**
     * 业务数据 加密密钥
     */
    @Value("${cybk.content.key}")
    private String contentKey;

    /**
     * MD5后的签名 加密密钥
     */
    @Value("${cybk.sign.key}")
    private String signKey;

    @Value("${cybk.oss.bucket}")
    private String cybkOssBucket;

    @Value("${cybk.credit.start}")
    private String creditStartTime;

    @Value("${cybk.credit.end}")
    private String creditEndTime;

    @Value("${cybk.loan.start}")
    private String loanStartTime;

    @Value("${cybk.loan.end}")
    private String loanEndTime;

    @Value("${cybk.repay.start}")
    private String repayStartTime;

    @Value("${cybk.repay.end}")
    private String repayEndTime;

    @Value("${cybk.guarantee.company.address}")
    private String guaranteeCompanyAddress;

    @Value("${cybk.repay.channel}")
    private String repayChannel;

    @Value("${cybk.sendImage.retries}")
    private Integer retries;

    @Value("${cybk.callBack.url}")
    private String callBackUrl;

    public  Integer getRetries() {return retries;}

    public String getRepayChannel() {
        return repayChannel;
    }

    public String getGuaranteeCompanyAddress() {
        return guaranteeCompanyAddress;
    }

    public String getChannelId() {
        return channelId;
    }

    public String getShortCode() {
        return shortCode;
    }

    public String getLoanType() {
        return loanType;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public String getMerchantShop() {
        return merchantShop;
    }

    public String getMerchantTerminal() {
        return merchantTerminal;
    }

    public String getCybkServerUrl() {
        return cybkServerUrl;
    }

    public String getSignKey() {
        return signKey;
    }

    public String getCybkOssBucket() {
        return cybkOssBucket;
    }

    public String getCreditStartTime() {
        return creditStartTime;
    }

    public String getCreditEndTime() {
        return creditEndTime;
    }

    public String getLoanStartTime() {
        return loanStartTime;
    }

    public String getLoanEndTime() {
        return loanEndTime;
    }

    public String getRepayStartTime() {
        return repayStartTime;
    }

    public String getRepayEndTime() {
        return repayEndTime;
    }

    public String getAppid() {
        return appid;
    }

    public String getChannel() {
        return channel;
    }

    public String getContentKey() {
        return contentKey;
    }

    public String getCallBackUrl() {return  callBackUrl;}
}
