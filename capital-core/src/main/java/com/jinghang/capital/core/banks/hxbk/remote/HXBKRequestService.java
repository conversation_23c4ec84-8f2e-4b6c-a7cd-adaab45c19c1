package com.jinghang.capital.core.banks.hxbk.remote;

import cn.com.antcloud.api.AntFinTechApiClient;
import cn.com.antcloud.api.AntFinTechProfile;
import cn.com.antcloud.api.antcloud.AntCloudClientRequest;
import cn.com.antcloud.api.antcloud.AntCloudClientResponse;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.capital.core.banks.cybk.enums.CybkResponseStatus;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKCommonData;
import com.jinghang.capital.core.banks.hxbk.dto.bind.HXBKBindCardApplyRequest;
import com.jinghang.capital.core.banks.hxbk.dto.bind.HXBKBindCardApplyResponse;
import com.jinghang.capital.core.banks.hxbk.dto.contract.HXBKContractGetRequest;
import com.jinghang.capital.core.banks.hxbk.dto.contract.HXBKContractGetResponse;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditQueryRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditQueryResponse;
import com.jinghang.capital.core.banks.hxbk.dto.loan.*;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayApplyRequest;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayApplyResponse;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayTrialRequest;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayTrialResponse;
import com.jinghang.capital.core.banks.hxbk.dto.repay.*;
import com.jinghang.capital.core.banks.hxbk.dto.file.HXBKSettlementCertificateRequest;
import com.jinghang.capital.core.banks.hxbk.dto.file.HXBKSettlementCertificateResponse;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditApplyRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditApplyResponse;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.common.http.exception.HttpErrorCode;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;

/**
 * 湖消蚂蚁天枢请求服务
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:15
 */
@Service
public class HXBKRequestService {
    private static final Logger logger = LoggerFactory.getLogger(HXBKRequestService.class);

    private final HXBKConfig config;

    public HXBKRequestService(HXBKConfig config) {
        this.config = config;
    }

    /**
     * 通用请求方法，基于AntFinTech API
     *
     * @param request   请求对象
     * @param respClass 响应类型
     * @return 响应数据
     * @throws HttpException           HTTP异常
     * @throws JsonProcessingException JSON处理异常
     */
    public <R> R request(HXBKBaseRequest request, Class<R> respClass) throws HttpException, JsonProcessingException,
            InvalidKeySpecException, NoSuchAlgorithmException, InvalidKeyException, SignatureException {
        // 唯一requestKey 方便查看日志 4个随机字符
        String requestKey = RandomUtil.randomStringUpper(4);
        // 公共参数转换
        HXBKCommonData commonData = fillCommonParams(request);

        // 创建profile，指定调用地址和蚂蚁区块链颁发的 Access ID 和 Access Secret
        AntFinTechProfile profile = AntFinTechProfile.getProfile(
                commonData.getBaseUrl(),
                commonData.getAk(),
                commonData.getSk());
        // 创建Client
        AntFinTechApiClient acApiClient = new AntFinTechApiClient(profile);
        // 创建Request
        AntCloudClientRequest antRequest = new AntCloudClientRequest();
        // 指定调用方法
        antRequest.setMethod(request.getMethod().getName());
        // 指定版本号
        antRequest.setVersion(request.getMethod().getVersion());

        // 将请求对象传入参数
        JSONObject object = JSONObject.from(request);
        antRequest.putParametersFromObject(object);

        String responseData;
        // 发送请求
        try {
            // 打印请求地址
            logger.info("【{}蚂蚁湖消HXBKRequestService】 请求对象: {}", requestKey, JSON.toJSONString(antRequest));
            AntCloudClientResponse response = acApiClient.execute(antRequest);
            // 获取返回值
            responseData = response.getData();
            logger.info("【{}蚂蚁湖消HXBKRequestService】 响应数据: {}", requestKey, responseData);
        } catch (Exception e) {
            logger.error("【{}蚂蚁湖消HXBKRequestService】 请求异常", requestKey, e);
            throw new HttpException(HttpErrorCode.valueOf("【" + requestKey + "】" + "蚂蚁湖消HXBK请求异常: " + e.getMessage()), e);
        }

        // 将响应数据转换为指定类型
        return JsonUtil.convertToObject(responseData, respClass);
    }

    /**
     * 2.1 天枢系统资金方代码（资金路由）查询
     *
     * @param request
     * @return
     */
    public HXBKFundRouterQueryResponse fundRouterQuery(HXBKFundRouterQueryRequest request) {
        try {
            return this.request(request, HXBKFundRouterQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("请求蚂蚁异常", bizEx);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            logger.error("请求蚂蚁异常，密钥签名问题", e);
        }
        return null;
    }

    /**
     * 2.2 天枢系统授信申请接口
     *
     * @param request 授信申请请求
     * @return 授信申请响应
     */
    public HXBKCreditApplyResponse creditApply(HXBKCreditApplyRequest request) {
        try {
            return this.request(request, HXBKCreditApplyResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("请求蚂蚁异常", bizEx);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            logger.error("请求蚂蚁异常，密钥签名问题", e);
        }
        return null;
    }

    /**
     * 2.3 天枢系统授信额度查询接口
     *
     * @param request 授信查询请求
     * @return 授信查询响应
     */
    public HXBKCreditQueryResponse creditQuery(HXBKCreditQueryRequest request) {
        try {
            return this.request(request, HXBKCreditQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("Json转换异常", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("Http请求异常", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (BizException bizEx) {
            logger.error("请求蚂蚁异常", bizEx);
            throw bizEx;
        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            logger.error("请求蚂蚁异常，密钥签名问题", e);
            throw new BizException(BizErrorCode.VERIFY_SIGN_ERROR);
        }
    }

    /**
     * 湖消调用还款试算接口
     *
     * @param request 请求参数
     * @return 试算后信息
     */
    public HXBKRepayTrialResponse repayTrial(HXBKRepayTrialRequest request) {
        try {
            return this.request(request, HXBKRepayTrialResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("湖消直连试算失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("湖消直连试算失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (BizException ex) {
            logger.error("湖消直连试算失败", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("湖消直连试算失败", ex);
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR);
        }
    }

    /**
     * 湖消调用还款接口
     *
     * @param request 请求参数
     * @return 还款结果信息
     */
    public HXBKRepayApplyResponse repayApply(HXBKRepayApplyRequest request) {
        try {
            return this.request(request, HXBKRepayApplyResponse.class);
        } catch (Exception bizEx) {
            logger.error("湖消直连 还款申请失败", bizEx);
        }
        return new HXBKRepayApplyResponse();
    }

    /**
     * 湖消调用还款结果查询接口
     *
     * @param request 请求参数
     * @return 还款结果查询返回信息
     */
    public HXBKRepayQueryResponse repayQuery(HXBKRepayQueryRequest request) {
        try {
            return this.request(request, HXBKRepayQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("湖消直连 还款结果查询失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("湖消直连 还款结果查询失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            logger.error("请求蚂蚁异常，密钥签名问题", e);
            throw new BizException(BizErrorCode.SUCH_INVALID_SIGN_ERROR);
        } catch (BizException e) {
            if (CybkResponseStatus.NOT_EXIST.getRespCode().equals(e.getCode())) {
                HXBKRepayQueryResponse response = new HXBKRepayQueryResponse();
                response.setRepayStatus("Fail");
                response.setFailReason("还款不存在");//失败原因
                return response;
            }
            logger.error("湖消直连 还款结果查询失败", e);
            throw e;
        }
    }

    /**
     * 公共参数转换
     *
     * @param request
     * @return
     */
    private HXBKCommonData fillCommonParams(HXBKBaseRequest request) {
        HXBKCommonData baseRemoteData = new HXBKCommonData();
        baseRemoteData.setBaseUrl(config.getBaseUrl());
        baseRemoteData.setAk(config.getAk());
        baseRemoteData.setSk(config.getSk());
        return baseRemoteData;
    }

    public HXBKLoanQueryResponse queryHXBKLoanQuery(HXBKLoanQueryRequest request) {

        HXBKLoanQueryResponse response = null;
        try {
            response = this.request(request, HXBKLoanQueryResponse.class);
        } catch (HttpException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 http异常", e);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (JsonProcessingException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 json转换异常", e);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (InvalidKeySpecException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 invalidKeySpec异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (NoSuchAlgorithmException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 NoSuchAlgorithm异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (InvalidKeyException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 InvalidKey异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (SignatureException e) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 Signature异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        }
        if (null == response) {
            logger.error("请求蚂蚁[放款结果查询接口]异常 结果为 null");
            throw new BizException(BizErrorCode.LOAN_RESULT_QUERY_ERROR);
        }

        return response;
    }

    public HXBKRepayListQueryResponse queryHXBKRepayList(HXBKRepayListQueryRequest request) {

        HXBKRepayListQueryResponse response = null;
        try {
            response = this.request(request, HXBKRepayListQueryResponse.class);
        } catch (HttpException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 http异常", e);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (JsonProcessingException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 json转换异常", e);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (InvalidKeySpecException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 invalidKeySpec异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (NoSuchAlgorithmException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 NoSuchAlgorithm异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (InvalidKeyException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 InvalidKey异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (SignatureException e) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 Signature异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        }
        if (null == response) {
            logger.error("请求蚂蚁[还款计划查询接口]异常 结果为 null");
            throw new BizException(BizErrorCode.LOAN_RESULT_QUERY_ERROR);
        }

        return response;
    }

    // 2.16 天枢系统合同获取接口
    public HXBKContractGetResponse getContract(HXBKContractGetRequest request) {
        HXBKContractGetResponse response = null;
        try {
            response = this.request(request, HXBKContractGetResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("HXBK合同获取失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException | InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException |
                 SignatureException he) {
            logger.error("HXBK合同获取失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }
        return response;
    }

    /**
     * 2.30 开具结清证明查询接口
     *
     * @param request 结清证明查询请求
     * @return 结清证明查询响应
     */
    public HXBKSettlementCertificateResponse settlementCertificateQuery(HXBKSettlementCertificateRequest request) {
        try {
            return this.request(request, HXBKSettlementCertificateResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("HXBK结清证明查询失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("HXBK结清证明查询失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            logger.error("HXBK结清证明查询失败，密钥签名问题", e);
            throw new BizException(BizErrorCode.SUCH_INVALID_SIGN_ERROR);
        } catch (BizException e) {
            logger.error("HXBK结清证明查询失败", e);
            throw e;
        } catch (Exception ex) {
            logger.error("HXBK结清证明查询失败", ex);
            throw new BizException(BizErrorCode.SETTLEMENT_CERTIFICATE_QUERY_ERROR);
        }
    }

    public HXBKLoanApplyResponse loanApply(HXBKLoanApplyRequest request) {
        HXBKLoanApplyResponse response = null;
        try {
            response = this.request(request, HXBKLoanApplyResponse.class);
        } catch (HttpException e) {
            logger.error("请求蚂蚁[用信接口]异常 http异常", e);
        } catch (JsonProcessingException e) {
            logger.error("请求蚂蚁[用信接口]异常 json转换异常", e);
        } catch (InvalidKeySpecException e) {
            logger.error("请求蚂蚁[用信接口]异常 invalidKeySpec异常", e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("请求蚂蚁[用信接口]异常 NoSuchAlgorithm异常", e);
        } catch (InvalidKeyException e) {
            logger.error("请求蚂蚁[用信接口]异常 InvalidKey异常", e);
        } catch (SignatureException e) {
            logger.error("请求蚂蚁[用信接口]异常 Signature异常", e);
        }
        return response;
    }


    public HXBKBindCardApplyResponse sign(HXBKBindCardApplyRequest hxbkBindRequest) {
        HXBKBindCardApplyResponse response = null;

        try {
            response = this.request(hxbkBindRequest, HXBKBindCardApplyResponse.class);

        } catch (HttpException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 http异常", e);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (JsonProcessingException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 json转换异常", e);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (InvalidKeySpecException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 invalidKeySpec异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (NoSuchAlgorithmException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 NoSuchAlgorithm异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (InvalidKeyException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 InvalidKey异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        } catch (SignatureException e) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 Signature异常", e);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        }

        if (null == response) {
            logger.error("请求蚂蚁[协议签约申请接口]异常 结果为null");
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY);
        }
        return response;
    }
}
