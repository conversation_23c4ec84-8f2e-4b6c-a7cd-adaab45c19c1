 package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

/**
 * HXBK授信额度信息
 * 主动查询和回调共用
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 10:05
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCreditAmount {

    /**
     * 授信额度
     * 必填
     */
    @JsonProperty("credit_amount")
    private BigDecimal creditAmount;

    /**
     * 授信余额
     * 必填
     */
    @JsonProperty("rest_amount")
    private BigDecimal restAmount;

    /**
     * 已用额度
     * 回调无此字段
     */
    @JsonProperty("used_amount")
    private BigDecimal usedAmount;

    /**
     * 当前额度
     * 回调无此字段
     */
    @JsonProperty("cur_amount")
    private BigDecimal curAmount;

    /**
     * 发放日期
     * 必填
     */
    @JsonProperty("pay_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date payDate;

    /**
     * 发放日期
     * 非必填，兼容字段 yyyy-MM-dd
     * 回调无此字段
     */
    @JsonProperty("pay_date_sup")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date payDateSup;

    /**
     * 到期日期
     * 必填
     */
    @JsonProperty("expire_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDate;

    /**
     * 到期日期
     * 非必填，兼容字段 yyyy-MM-dd
     * 回调无此字段
     */
    @JsonProperty("expire_date_sup")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDateSup;

    /**
     * 利率单位
     * 非必填
     * 1:年，2:月，3:日
     */
    @JsonProperty("rate_unit")
    private String rateUnit;

    /**
     * 执行利率
     * 必填
     * 授信年利率。精确到小数点后四位0.1250，表示年利率为12.5%（成功时返）
     */
    @JsonProperty("rate_value")
    private BigDecimal rateValue;

    /**
     * 还款方式
     * 非必填
     * 1等额本息 2等额本金 3先息后本 4一次性利随本清 5只还本金 6等本等息
     */
    @JsonProperty("repay_way")
    private String repayWay;

    /**
     * 状态
     * 必填
     * 0-正常 1-冻结 2-过期
     */
    @JsonProperty("status")
    private String status;

    // Getter and Setter methods
    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public BigDecimal getRestAmount() {
        return restAmount;
    }

    public void setRestAmount(BigDecimal restAmount) {
        this.restAmount = restAmount;
    }

    public BigDecimal getUsedAmount() {
        return usedAmount;
    }

    public void setUsedAmount(BigDecimal usedAmount) {
        this.usedAmount = usedAmount;
    }

    public BigDecimal getCurAmount() {
        return curAmount;
    }

    public void setCurAmount(BigDecimal curAmount) {
        this.curAmount = curAmount;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Date getPayDateSup() {
        return payDateSup;
    }

    public void setPayDateSup(Date payDateSup) {
        this.payDateSup = payDateSup;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getExpireDateSup() {
        return expireDateSup;
    }

    public void setExpireDateSup(Date expireDateSup) {
        this.expireDateSup = expireDateSup;
    }

    public String getRateUnit() {
        return rateUnit;
    }

    public void setRateUnit(String rateUnit) {
        this.rateUnit = rateUnit;
    }

    public BigDecimal getRateValue() {
        return rateValue;
    }

    public void setRateValue(BigDecimal rateValue) {
        this.rateValue = rateValue;
    }

    public String getRepayWay() {
        return repayWay;
    }

    public void setRepayWay(String repayWay) {
        this.repayWay = repayWay;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
