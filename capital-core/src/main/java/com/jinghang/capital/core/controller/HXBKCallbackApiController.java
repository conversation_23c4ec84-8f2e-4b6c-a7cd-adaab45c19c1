package com.jinghang.capital.core.controller;

import cn.hutool.core.util.StrUtil;
import com.jinghang.capital.api.HXBKCallbackApiService;
import com.jinghang.capital.core.banks.hxbk.callback.dto.MethodResponse;
import com.jinghang.capital.core.banks.hxbk.callback.service.HXBKCallbackService;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKCallbackResponseCode;
import com.jinghang.capital.core.banks.hxbk.util.HXBKCryptoUtil;
import com.jinghang.capital.core.banks.hxbk.util.crypto.AESUtils;
import com.jinghang.capital.core.banks.hxbk.util.crypto.ParamUtils;
import com.jinghang.capital.core.banks.hxbk.util.crypto.RSAUtils;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.TreeMap;

/**
 * HXBK回调接口控制器
 * 处理蚂蚁天枢系统的回调请求
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 22:00
 */
@RestController
@RequestMapping("/hxbk/callback")
public class HXBKCallbackApiController implements HXBKCallbackApiService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKCallbackApiController.class);

    @Autowired
    private HXBKCryptoUtil cryptoUtil;

    @Autowired
    private HXBKCallbackService callbackService;

    /**
     * 统一回调接口
     * 根据请求中的method字段分发到不同的业务处理逻辑
     *
     * @param requestBody 蚂蚁侧加密请求体
     * @return 加密响应
     */
    @Override
    public String handle(@RequestBody String requestBody) {
        logger.info("收到HXBK回调请求，请求参数：{}", requestBody);

        try {
            // 步骤1：解析请求JSON
            @SuppressWarnings("unchecked")
            Map<String, Object> requestJson = JsonUtil.convertToObject(requestBody, Map.class);

            // 步骤2：验签
            TreeMap<String, String> paramsToVerify = ParamUtils.parseParamFromRequest(requestJson);
            String toVerifyStr = paramsToVerify.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(java.util.stream.Collectors.joining("&"));

            String signature = (String) requestJson.get("sign");
            boolean verified = RSAUtils.verifyData(signature, toVerifyStr, cryptoUtil.getAntPublicKey());

            if (!verified) {
                logger.error("HXBK请求验签失败");
                HXBKCallbackResponseCode responseCode = HXBKCallbackResponseCode.PARAM_ERROR;
                return cryptoUtil.buildFailResponse(responseCode.getCode(), "请求参数错误:验签失败");
            }

            // 步骤3：解密SecretKey
            String encryptedSecretKey = (String) requestJson.get("secretKey");
            String secretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, cryptoUtil.getPartnerPrivateKey());

            // 步骤4：解密业务数据
            String encryptedData = (String) requestJson.get("data");
            String bizDataStr = AESUtils.decryptDataWithAES(encryptedData, secretKey);
            logger.info("HXBK回调请求解密成功，解密业务数据: {}", bizDataStr);

            // 步骤5：解析公共参数
            String method = (String) requestJson.get("method");
            String requestId = (String) requestJson.get("requestId");

            // 步骤6：根据method分发到具体的业务处理
            MethodResponse responseData = callbackService.processCallback(method, requestId, bizDataStr);

            // 步骤7：构建成功响应
            return cryptoUtil.buildSuccessResponse(responseData);

        } catch (Exception e) {
            logger.error("处理HXBK回调请求失败", e);

            // 根据异常类型返回对应的响应码
            HXBKCallbackResponseCode responseCode = HXBKCallbackResponseCode.fromException(e);

            // 如果是业务异常且异常消息不为空，使用实际的异常消息；否则使用枚举的固定消息
            String responseMessage;
            if (responseCode == HXBKCallbackResponseCode.BUSINESS_ERROR && StrUtil.isNotBlank(e.getMessage())) {
                responseMessage = e.getMessage();
            } else {
                responseMessage = responseCode.getMsg();
            }

            return cryptoUtil.buildFailResponse(responseCode.getCode(), responseMessage);
        }
    }
}
