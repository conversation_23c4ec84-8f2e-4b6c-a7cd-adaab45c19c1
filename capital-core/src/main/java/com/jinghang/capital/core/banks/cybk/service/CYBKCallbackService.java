package com.jinghang.capital.core.banks.cybk.service;

import com.alibaba.fastjson.JSONObject;
import com.jinghang.capital.core.banks.*;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.repository.QuotaAdjustRecordRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.service.repay.FinRepayService;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR> gale
 * @Classname CYBKCallbackService
 * @Description
 * @Date 2025/5/14 00:53
 */
@Service
public class CYBKCallbackService extends AbstractBankCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCallbackService.class);
    @Autowired
    private QuotaAdjustRecordRepository quotaAdjustRecordRepository;
    @Autowired
    private FinRepayService finRepayService;
    @Autowired
    private WarningService warningService;
    @Autowired
    private CommonService commonService;
    @Autowired
    @Qualifier("CYBKCreditService")
    private BankCreditService bankCreditService;
    @Autowired
    @Qualifier("CYBKLoanService")
    private BankLoanService bankLoanService;
    @Autowired
    private FinCreditService finCreditService;
    @Autowired
    private BankQuotaService bankQuotaService;
    @Qualifier("CYBKRepayService")
    @Autowired
    private BankRepayService bankRepayService;


    /**
     * 授信结果回调
     *
     * @param json
     */
    @Override
    public String creditResultCallback(String json) {
        logger.info("长银授信结果回调入参：{}", json);
        JSONObject resJson = new JSONObject();//响应json
        resJson.put("resultCode", "01");

        JSONObject jsonObject = JSONObject.parseObject(json);
        //外部授信流水号即为授信表主键id
        if (!hasKeyWithValue(jsonObject,"outApplSeq")){
            resJson.put("resultDesc", "外部授信流水号outApplSeq不能为空");
            return resJson.toJSONString();
        }
        String creditId = jsonObject.getString("outApplSeq");

        //获取回调状态
        if (!hasKeyWithValue(jsonObject,"outSts")){
            resJson.put("resultDesc", "外部状态outSts不能为空");
            return resJson.toJSONString();
        }
        ProcessStatus creditCallBackResultStatus;
        String outSts = jsonObject.getString("outSts");
        switch (outSts) {
            case "01":
                resJson.put("resultDesc", "接收成功");
                return resJson.toJSONString();
            case "02":
                creditCallBackResultStatus = ProcessStatus.FAIL;
                break;
            case "03":
                creditCallBackResultStatus = ProcessStatus.SUCCESS;
                break;
            default:
                getWarningService().warn("未知回调授信状态，授信表id：" + creditId + "，outSts：" + outSts);
                resJson.put("resultDesc", "未知回调授信状态：" + outSts);
                return resJson.toJSONString();
        }

        //主动查询
        CreditQueryVo apply = new CreditQueryVo();
        apply.setCreditId(creditId);
        getBankCreditService().query(apply);
        //主动查询后重新获取授信数据表
        Credit credit = getFinCreditService().getCredit(creditId);
        String creditSts = credit.getCreditStatus().name();//主动查询状态
        //判断回调状态和主动查询状态是否一致
        if (!creditSts.equals(creditCallBackResultStatus.name())){
            //不一致
            getWarningService().warn("授信回调状态和主动查询状态不一致，授信creditId：" + creditId + "，回调状态：" + creditCallBackResultStatus.name() + "，主动查询状态：" + creditSts);

            resJson.put("resultDesc","授信回调状态和主动查询状态不一致");
            return resJson.toJSONString();
        }
        //返回结果
        resJson.put("resultDesc","接收成功");
        logger.info("长银授信结果回调出参：{}", resJson.toJSONString());
        return resJson.toJSONString();
    }

    /**
     * 放款结果回调
     *
     * @param json
     */
    @Override
    public String loanResultCallback(String json) {
        logger.info("长银放款结果回调入参：{}", json);
        JSONObject resJson = new JSONObject();//响应json
        resJson.put("resultCode", "01");

        JSONObject jsonObject = JSONObject.parseObject(json);
        //外部放款流水号即为借款表的主键id
        if (!hasKeyWithValue(jsonObject,"outLoanSeq")){
            resJson.put("resultDesc", "外部放款流水号outLoanSeq不能为空");
            return resJson.toJSONString();
        }
        String loanId = jsonObject.getString("outLoanSeq");
        //获取回调状态
        if (!hasKeyWithValue(jsonObject,"dnSts")){
            resJson.put("resultDesc", "放款状态dnSts不能为空");
            return resJson.toJSONString();
        }
        ProcessStatus loanCallBackResultStatus;
        String dnSts = jsonObject.getString("dnSts");
        switch (dnSts) {
            case "100":
                resJson.put("resultDesc", "接收成功");
                return resJson.toJSONString();
            case "200":
                loanCallBackResultStatus = ProcessStatus.SUCCESS;
                break;
            case "300":
                loanCallBackResultStatus = ProcessStatus.FAIL;
                break;
            default:
                getWarningService().warn("未知放款结果回调状态，借款表id：" + loanId + "，dnSts：" + dnSts);
                resJson.put("resultDesc", "未知放款结果回调状态：" + dnSts);
                return resJson.toJSONString();
        }

        //主动查询
        LoanQueryVo query = new LoanQueryVo(loanId);
        getBankLoanService().query(query);
        //主动查询后重新获取借款表数据
        Loan loan = getCommonService().findLoanById(loanId);
        String loanSts = loan.getLoanStatus().name();//主动查询状态
        //判断回调状态和主动查询状态是否一致
        if (!loanSts.equals(loanCallBackResultStatus.name())){
            //不一致
            getWarningService().warn("放款结果回调状态和主动查询状态不一致，借款loanId：" + loanId + "，回调状态：" + loanCallBackResultStatus.name() + "，主动查询状态：" + loanSts);

            resJson.put("resultDesc","放款结果回调状态和主动查询状态不一致");
            return resJson.toJSONString();
        }
        //返回结果
        resJson.put("resultDesc","接收成功");
        logger.info("长银放款结果回调出参：{}", resJson.toJSONString());
        return resJson.toJSONString();
    }

    /**
     * 调额结果回调
     *
     * @param json
     */
    @Override
    public String quotaAdjustResultCallback(String json) {
        logger.info("长银调额结果回调入参：{}", json);
        JSONObject resJson = new JSONObject();//响应json
        resJson.put("status", "01");

        JSONObject jsonObject = JSONObject.parseObject(json);
        if (!hasKeyWithValue(jsonObject,"adjustNo")){
            resJson.put("statusDesc", "长银调额流水号adjustNo不能为空");
            return resJson.toJSONString();
        }
        if (!hasKeyWithValue(jsonObject,"outAdjustNo")){
            resJson.put("statusDesc", "合作方调额流水号outAdjustNo不能为空");
            return resJson.toJSONString();
        }
        //响应参数
        resJson.put("adjustNo",jsonObject.getString("adjustNo"));
        resJson.put("outAdjustNo",jsonObject.getString("outAdjustNo"));

        //回调合作方调额流水号即为循环额度调额记录表主键id
        String adjustId = jsonObject.getString("outAdjustNo");
        //获取回调状态
        if (!hasKeyWithValue(jsonObject,"adjustStatus")){
            resJson.put("statusDesc", "调额结果adjustStatus不能为空");
            return resJson.toJSONString();
        }
        ProcessStatus quotaCallBackResultStatus;
        String adjustStatus = jsonObject.getString("adjustStatus");
        switch (adjustStatus) {
            case "02":
                quotaCallBackResultStatus = ProcessStatus.FAIL;
                break;
            case "01":
                quotaCallBackResultStatus = ProcessStatus.SUCCESS;
                break;
            default:
                getWarningService().warn("未知调额结果回调状态，循环额度调额记录表id：" + adjustId + "，adjustStatus：" + adjustStatus);
                resJson.put("statusDesc", "未知调额结果回调状态：" + adjustStatus);
                return resJson.toJSONString();
        }
        //主动查询
        getBankQuotaService().quotaAdjustResultQuery(adjustId);
        //主动查询后重新获取循环额度调额记录表数据
        QuotaAdjustRecord record = quotaAdjustRecordRepository.findById(adjustId).orElse(null);
        String recordSts = record.getStatus().name();//主动查询状态
        //判断回调状态和主动查询状态是否一致
        if (!recordSts.equals(quotaCallBackResultStatus.name())){
            //不一致
            getWarningService().warn("调额结果回调状态和主动查询状态不一致，调额申请id：" + adjustId + "，回调状态：" + quotaCallBackResultStatus.name() + "，主动查询状态：" + recordSts);

            resJson.put("statusDesc","调额结果回调状态和主动查询状态不一致");
            return resJson.toJSONString();
        }
        //返回结果
        resJson.put("statusDesc","接收成功");
        logger.info("长银调额结果回调出参：{}", resJson.toJSONString());
        return resJson.toJSONString();
    }

    /**
     * 还款结果回调
     *
     * @param json
     */
    @Override
    public String repayResultCallback(String json) {
        logger.info("长银还款结果回调入参：{}", json);
        JSONObject resJson = new JSONObject();//响应json
        resJson.put("resultCode", "01");

        JSONObject jsonObject = JSONObject.parseObject(json);
        //外部还款流水号即为对资还款记录表主键id
        if (!hasKeyWithValue(jsonObject,"outRepaySeq")){
            resJson.put("resultDesc", "外部还款流水号outRepaySeq不能为空");
            return resJson.toJSONString();
        }
        String repayId = jsonObject.getString("outRepaySeq");
        //获取回调状态
        if (!hasKeyWithValue(jsonObject,"repayStatus")){
            resJson.put("resultDesc", "还款结果repayStatus不能为空");
            return resJson.toJSONString();
        }
        ProcessStatus repayCallBackResultStatus;
        String repayStatus = jsonObject.getString("repayStatus");
        switch (repayStatus) {
            case "01":
                repayCallBackResultStatus = ProcessStatus.SUCCESS;
                break;
            case "02":
                repayCallBackResultStatus = ProcessStatus.FAIL;
                break;
            case "03":
                resJson.put("resultDesc", "接收成功");
                return resJson.toJSONString();
            default:
                getWarningService().warn("未知还款结果回调状态，对资还款记录表id：" + repayId + "，repayStatus：" + repayStatus);
                resJson.put("resultDesc", "未知还款结果回调状态：" + repayStatus);
                return resJson.toJSONString();
        }

        //主动查询
        getBankRepayService().query(repayId);
        //主动查询后重新获取对资还款记录表数据
        BankRepayRecord bankRepayRecord = getFinRepayService().getBankRepayRecord(repayId);
        String bankRepayRecordSts = bankRepayRecord.getRepayStatus().name();//主动查询状态
        //判断回调状态和主动查询状态是否一致
        if (!bankRepayRecordSts.equals(repayCallBackResultStatus.name())){
            //不一致
            getWarningService().warn("放款结果回调状态和主动查询状态不一致，对资还款记录表id：" + repayId + "，回调状态：" + repayCallBackResultStatus.name() + "，主动查询状态：" + bankRepayRecordSts);

            resJson.put("resultDesc","放款结果回调状态和主动查询状态不一致");
            return resJson.toJSONString();
        }
        //返回结果
        resJson.put("resultDesc","接收成功");
        logger.info("长银还款结果回调出参：{}", resJson.toJSONString());
        return resJson.toJSONString();
    }

    /**
     * 是否支持该资方渠道
     *
     * @param channel 资方渠道
     * @return 支持情况
     */
    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }

    public static boolean hasKeyWithValue(JSONObject json, String key) {
        if (!json.containsKey(key)) {
            return false; // Key 不存在
        }

        Object value = json.get(key);

        // 根据具体类型判断是否“有值”
        if (value == null) {
            return false; // 排除 null
        }

        if (value instanceof String) {
            return !((String) value).isEmpty(); // 字符串非空
        }

        if (value instanceof Collection) {
            return !((Collection<?>) value).isEmpty(); // 集合非空
        }

        if (value instanceof Map) {
            return !((Map<?, ?>) value).isEmpty(); // Map 非空
        }

        if (value instanceof Object[]) {
            return ((Object[]) value).length > 0; // 数组非空
        }

        // 其他类型（如 Number、Boolean 等）直接视为有值
        return true;
    }


    public FinRepayService getFinRepayService() {
        return finRepayService;
    }
    public WarningService getWarningService() {
        return warningService;
    }
    protected CommonService getCommonService() {
        return commonService;
    }
    protected BankCreditService getBankCreditService() {
        return bankCreditService;
    }
    protected BankLoanService getBankLoanService() {
        return bankLoanService;
    }
    protected FinCreditService getFinCreditService() {
        return finCreditService;
    }
    protected BankQuotaService getBankQuotaService() {
        return bankQuotaService;
    }
    protected BankRepayService getBankRepayService() {
        return bankRepayService;
    }
}
