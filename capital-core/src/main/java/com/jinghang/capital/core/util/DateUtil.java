package com.jinghang.capital.core.util;



import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

public final class DateUtil {


    /**
     * 计算资金占用天数
     *
     * @param loan：借据信息
     * @param minPlan：上期还款日
     * @return 资金占用天数
     */
    public static long calBetweenDays(Loan loan, LoanReplan minPlan) {
        long days;
        if (minPlan.getPeriod().equals(1)) {
            //放款日到当前
            days = ChronoUnit.DAYS.between(loan.getLoanTime().toLocalDate(), LocalDate.now());
        } else {
            //上期还款日到今天
            days = ChronoUnit.DAYS.between(minPlan.getRepayDate(), LocalDate.now());
        }
        return days;
    }

    /**
     * 时间是否在今天之内
     */
    public static Boolean betweenToday(LocalDateTime time) {
        return time.isAfter(getTodayStartTime()) && time.isBefore(todayEndTime());
    }

    /**
     * 获取当天结束时间
     */
    public static LocalDateTime todayEndTime() {
        LocalDateTime localDateTime = LocalDateTime.now();
        return endDateTime(localDateTime);
    }

    /**
     * 获取当天结束时间
     */
    public static LocalDateTime endDateTime(LocalDateTime localDateTime) {
        return endDateTime(localDateTime.toLocalDate());
    }

    /**
     * 结束时间 yyyy-MM-dd 23:59:59
     */
    public static LocalDateTime endDateTime(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            return null;
        }
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }


    /**
     * 格式化时间
     */
    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        return formatLocalDateTime(localDateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime, DateTimeFormatter formatter) {
        return localDateTime.format(formatter);
    }

    public static String formatLocalDate(LocalDate localDate) {
        return formatLocalDate(localDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public static String formatLocalDate(LocalDate localDate, DateTimeFormatter formatter) {
        return localDate.format(formatter);
    }

    /**
     * 获取当天开始时间
     */
    public static LocalDateTime getTodayStartTime() {
        return stateDateTime(LocalDate.now());
    }

    public static LocalDateTime getTodayStartTimeStr() {
        return stateDateTime(LocalDate.now());
    }

    /**
     * 获取指定的开始时间
     */
    public static LocalDateTime stateDateTime(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     *  获取两天差值
     */
    public static long dateDiff(LocalDate stateDate, LocalDate endDate) {
        return stateDate.until(endDate, ChronoUnit.DAYS);
    }

}
