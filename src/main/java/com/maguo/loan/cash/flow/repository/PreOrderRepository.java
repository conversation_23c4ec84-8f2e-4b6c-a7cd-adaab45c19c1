package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface PreOrderRepository extends JpaRepository<PreOrder, String> {

    Optional<PreOrder> findByOrderNoAndFlowChannel(String orderNo, FlowChannel flowChannel);

    Optional<PreOrder> findByOrderNoAndFlowChannelAndApplicationSource(String orderNo, FlowChannel flowChannel, ApplicationSource applicationSource);

    Boolean existsByCertNoAndFlowChannel(String certNo, FlowChannel flowChannel);

    Optional<PreOrder> findByRiskId(String riskId);

    List<PreOrder> findByCertNoAndFlowChannelOrderByCreatedTime(String certNo, FlowChannel flowChannel);

    PreOrder findTopByOpenIdOrderByCreatedTimeDesc(String userId);

    List<PreOrder> findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(String userId, PreOrderState preOrderState);

    List<PreOrder> findByApplyTimeBetweenAndPreOrderStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);

    PreOrder findTopByOpenIdAndFlowChannelOrderByCreatedTimeDesc(String userId,FlowChannel flowChannel);

    @Query("SELECT SUM(p.applyAmount) FROM PreOrder p WHERE p.flowChannel = :flowChannel AND p.bankChannel = :bankChannel AND p.preOrderState IN :preOrderState AND p.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByDimensionsAndStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("bankChannel") BankChannel bankChannel,
        @Param("preOrderState") PreOrderState preOrderState,
        @Param("startTime") LocalDateTime todayStart,
        @Param("endTime") LocalDateTime todayEnd);


    @Query("SELECT SUM(p.applyAmount) FROM PreOrder p WHERE p.flowChannel = :flowChannel AND p.preOrderState IN :preOrderState AND p.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("preOrderState") PreOrderState preOrderState,
        @Param("startTime") LocalDateTime todayStart,
        @Param("endTime") LocalDateTime todayEnd);
}
