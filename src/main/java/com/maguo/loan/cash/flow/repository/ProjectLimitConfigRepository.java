package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.ProjectLimitConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ProjectLimitConfigRepository extends JpaRepository<ProjectLimitConfig, String> {

    /**
     * 根据核心四维度查询唯一的限额配置
     * @param flowChannel 资产方
     * @param guaranteeComp 融担方
     * @param bankChannel 资金方
     * @return 查询到的配置 Optional
     */
    Optional<ProjectLimitConfig> findByFlowChannelAndGuaranteeCompAndBankChannel(
        String flowChannel,
        String guaranteeComp,
        String bankChannel
    );

    /**
     * 根据核心四维度查询唯一的限额配置
     * @param flowChannel 资产方
     * @param bankChannel 资金方
     * @return 查询到的配置 Optional
     */
    Optional<ProjectLimitConfig> findByFlowChannelAndBankChannel(
        String flowChannel,
        String bankChannel
    );


    /**
     * 根据核心四维度查询唯一的限额配置
     * @param flowChannel 资产方
     */
    Optional<ProjectLimitConfig> findByFlowChannel(
        String flowChannel
    );
}
