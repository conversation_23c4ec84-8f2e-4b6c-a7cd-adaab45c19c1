import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化的测试类，用于验证JSONObject.from()和@JsonIgnore的行为
 * 不依赖外部库，直接测试核心逻辑
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("=== 开始测试 ===");
        
        // 模拟测试对象
        TestRequest request = new TestRequest();
        
        // 1. 测试反射获取方法
        System.out.println("1. 测试反射获取方法:");
        try {
            Method getMethodMethod = request.getClass().getMethod("getMethod");
            Object result = getMethodMethod.invoke(request);
            System.out.println("通过反射调用getMethod(): " + result);
            
            if (result instanceof TestMethod) {
                TestMethod method = (TestMethod) result;
                System.out.println("method.getName(): " + method.getName());
                System.out.println("method.getVersion(): " + method.getVersion());
            }
        } catch (Exception e) {
            System.out.println("反射调用失败: " + e.getMessage());
        }
        
        // 2. 测试字段序列化行为
        System.out.println("\n2. 测试字段序列化行为:");
        Map<String, Object> fieldMap = new HashMap<>();
        
        // 模拟JSONObject.from()的行为 - 通过反射获取所有字段
        java.lang.reflect.Field[] fields = request.getClass().getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            field.setAccessible(true);
            try {
                // 检查是否有@JsonIgnore注解（这里简化处理）
                boolean hasJsonIgnore = field.getName().equals("method"); // 模拟@JsonIgnore
                if (!hasJsonIgnore) {
                    Object value = field.get(request);
                    if (value != null) {
                        fieldMap.put(field.getName(), value);
                    }
                }
            } catch (Exception e) {
                System.out.println("获取字段值失败: " + field.getName() + " - " + e.getMessage());
            }
        }
        
        // 模拟JSONObject.from()的行为 - 通过getter方法获取值
        Method[] methods = request.getClass().getMethods();
        for (Method method : methods) {
            String methodName = method.getName();
            if (methodName.startsWith("get") && method.getParameterCount() == 0) {
                try {
                    // 检查方法是否有@JsonIgnore注解（这里简化处理）
                    boolean hasJsonIgnore = methodName.equals("getMethod"); // 模拟@JsonIgnore
                    if (!hasJsonIgnore) {
                        Object value = method.invoke(request);
                        if (value != null) {
                            String propertyName = methodName.substring(3);
                            propertyName = propertyName.substring(0, 1).toLowerCase() + propertyName.substring(1);
                            fieldMap.put(propertyName, value);
                        }
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
            }
        }
        
        System.out.println("模拟序列化后的字段:");
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 3. 测试删除字段的效果
        System.out.println("\n3. 测试删除method和version字段:");
        fieldMap.remove("method");
        fieldMap.remove("version");
        
        System.out.println("删除后的字段:");
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 4. 模拟AntCloudClientRequest的parameters
        System.out.println("\n4. 模拟AntCloudClientRequest的parameters:");
        Map<String, Object> parameters = new HashMap<>(fieldMap);
        
        System.out.println("最终parameters内容:");
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        boolean containsMethod = parameters.containsKey("method");
        boolean containsVersion = parameters.containsKey("version");
        
        System.out.println("\n=== 检查结果 ===");
        System.out.println("parameters是否包含'method'字段: " + containsMethod);
        System.out.println("parameters是否包含'version'字段: " + containsVersion);
        
        if (containsMethod) {
            System.out.println("parameters中的method值: " + parameters.get("method"));
        }
        if (containsVersion) {
            System.out.println("parameters中的version值: " + parameters.get("version"));
        }
        
        System.out.println("\n=== 结论 ===");
        if (!containsMethod && !containsVersion) {
            System.out.println("✓ 删除method和version字段成功，parameters中不包含这些字段");
        } else {
            System.out.println("✗ 删除method和version字段失败，parameters中仍包含这些字段");
        }
    }
    
    // 模拟测试请求类
    static class TestRequest {
        private String orderNo = "TEST_ORDER_001";
        private String channelType = "1";
        private TestMethod method = new TestMethod("riskplus.dubbridge.credit.apply", "1.0");
        
        public String getOrderNo() {
            return orderNo;
        }
        
        public String getChannelType() {
            return channelType;
        }
        
        // 模拟@JsonIgnore注解的方法
        public TestMethod getMethod() {
            return method;
        }
    }
    
    // 模拟方法枚举
    static class TestMethod {
        private String name;
        private String version;
        
        public TestMethod(String name, String version) {
            this.name = name;
            this.version = version;
        }
        
        public String getName() {
            return name;
        }
        
        public String getVersion() {
            return version;
        }
        
        @Override
        public String toString() {
            return "TestMethod{name='" + name + "', version='" + version + "'}";
        }
    }
}
