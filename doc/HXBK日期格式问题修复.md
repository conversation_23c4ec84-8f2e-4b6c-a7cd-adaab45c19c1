# HXBK日期格式问题修复

## 问题描述

在HXBK授信查询接口调用过程中，出现JSON反序列化异常：

```
com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "2025-07-29T00:00:00": expected format "yyyy-MM-dd HH:mm:ss"
```

## 问题分析

### 错误原因
1. **期望格式**：`yyyy-MM-dd HH:mm:ss` (如：`2025-07-29 00:00:00`)
2. **实际格式**：`yyyy-MM-dd'T'HH:mm:ss` (如：`2025-07-29T00:00:00`)

### 响应数据示例
```json
{
  "credit_amt": 5000.00,
  "result_msg": "Success",
  "custom_no": "PCM202507291950154874293166082",
  "credit_info": {
    "expire_date_sup": "2026-07-29",
    "pay_date": "2025-07-29T00:00:00",
    "rest_amount": 5000.00,
    "expire_date": "2026-07-29T00:00:00",
    "credit_amount": 5000.00,
    "pay_date_sup": "2025-07-29",
    "rate_unit": "1",
    "rate_value": 0.239900,
    "status": "0"
  },
  "req_msg_id": "74d204f5cfbe4b0f8d684d41e8ee62df",
  "apply_no": "CRD202507290000082",
  "result_code": "OK",
  "fund_code": "D20250701000000001",
  "status": "0"
}
```

### 涉及字段
- `pay_date`: `"2025-07-29T00:00:00"` (ISO 8601格式)
- `expire_date`: `"2026-07-29T00:00:00"` (ISO 8601格式)
- `pay_date_sup`: `"2025-07-29"` (日期格式)
- `expire_date_sup`: `"2026-07-29"` (日期格式)

## 解决方案

修改 `HXBKCreditAmount.java` 中的日期字段格式注解：

### 修改前
```java
@JsonProperty("pay_date")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private Date payDate;

@JsonProperty("expire_date")
private Date expireDate;

@JsonProperty("expire_date_sup")
private Date expireDateSup;
```

### 修改后
```java
@JsonProperty("pay_date")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
private Date payDate;

@JsonProperty("expire_date")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
private Date expireDate;

@JsonProperty("expire_date_sup")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date expireDateSup;
```

## 修改内容

1. **pay_date字段**：格式从 `yyyy-MM-dd HH:mm:ss` 改为 `yyyy-MM-dd'T'HH:mm:ss`
2. **expire_date字段**：添加 `@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")` 注解
3. **expire_date_sup字段**：添加 `@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")` 注解

## 验证

修改后的代码能够正确解析HXBK返回的日期格式：
- ISO 8601格式：`2025-07-29T00:00:00`
- 标准日期格式：`2025-07-29`

## 注意事项

1. **时区设置**：所有日期字段都使用 `GMT+8` 时区
2. **格式一致性**：确保与HXBK接口返回的实际格式保持一致
3. **兼容性**：`pay_date_sup` 字段保持原有的 `yyyy-MM-dd` 格式不变

## 相关文件

- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/credit/HXBKCreditAmount.java`

## 修复时间

2025-07-29
