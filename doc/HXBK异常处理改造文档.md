# HXBK回调接口异常处理改造文档

## 改造背景

原有的异常处理逻辑过于复杂，根据异常消息内容判断不同的错误类型（参数错误、业务异常、系统异常）。现需要简化为只区分系统异常和业务异常两种类型。

## 改造需求

1. **简化异常分类**：错误只分系统异常和业务异常
2. **系统异常条件**：exception为空 或 message为空
3. **业务异常条件**：message不为空
4. **消息处理**：业务异常时将实际的exception message放入返回值中

## 改造内容

### 1. 修改HXBKCallbackResponseCode枚举

**文件路径**: `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/enums/HXBKCallbackResponseCode.java`

**修改前**:
```java
public static HXBKCallbackResponseCode fromException(Exception exception) {
    if (exception == null) {
        return SYSTEM_ERROR;
    }

    String message = exception.getMessage();
    if (message == null) {
        return SYSTEM_ERROR;
    }

    if (message.contains("验签失败") || message.contains("解密失败") || message.contains("参数")) {
        return PARAM_ERROR;
    } else if (message.contains("不支持的方法")) {
        return BUSINESS_ERROR;
    } else {
        return SYSTEM_ERROR;
    }
}
```

**修改后**:
```java
public static HXBKCallbackResponseCode fromException(Exception exception) {
    if (exception == null) {
        return SYSTEM_ERROR;
    }

    String message = exception.getMessage();
    if (message == null || message.trim().isEmpty()) {
        return SYSTEM_ERROR;
    }

    // message不为空，返回业务异常
    return BUSINESS_ERROR;
}
```

### 2. 修改控制器异常处理逻辑

**文件路径**: `capital-core/src/main/java/com/jinghang/capital/core/controller/HXBKCallbackApiController.java`

**修改前**:
```java
} catch (Exception e) {
    logger.error("处理HXBK回调请求失败", e);

    // 根据异常类型返回对应的响应码
    HXBKCallbackResponseCode responseCode = HXBKCallbackResponseCode.fromException(e);
    return cryptoUtil.buildFailResponse(responseCode.getCode(), responseCode.getMsg());
}
```

**修改后**:
```java
} catch (Exception e) {
    logger.error("处理HXBK回调请求失败", e);

    // 根据异常类型返回对应的响应码
    HXBKCallbackResponseCode responseCode = HXBKCallbackResponseCode.fromException(e);
    
    // 如果是业务异常且异常消息不为空，使用实际的异常消息；否则使用枚举的固定消息
    String responseMessage;
    if (responseCode == HXBKCallbackResponseCode.BUSINESS_ERROR && 
        e.getMessage() != null && !e.getMessage().trim().isEmpty()) {
        responseMessage = e.getMessage();
    } else {
        responseMessage = responseCode.getMsg();
    }
    
    return cryptoUtil.buildFailResponse(responseCode.getCode(), responseMessage);
}
```

## 改造效果

### 1. 异常分类逻辑

| 异常情况 | 返回码 | 返回消息 |
|---------|--------|----------|
| exception为null | 200000 | "系统异常" |
| exception.getMessage()为null | 200000 | "系统异常" |
| exception.getMessage()为空字符串 | 200000 | "系统异常" |
| exception.getMessage()不为空 | 300000 | 实际的异常消息 |

### 2. 示例场景

**场景1：空指针异常**
```java
Exception e = new NullPointerException();
// 返回: code=200000, msg="系统异常"
```

**场景2：业务异常**
```java
Exception e = new RuntimeException("用户不存在");
// 返回: code=300000, msg="用户不存在"
```

**场景3：自定义业务异常**
```java
Exception e = new BusinessException("订单状态不正确，无法进行此操作");
// 返回: code=300000, msg="订单状态不正确，无法进行此操作"
```

## 优势

1. **逻辑简化**：不再需要根据消息内容判断具体的错误类型
2. **信息保留**：业务异常时保留原始的异常消息，便于问题定位
3. **统一处理**：所有异常都按照统一的规则进行分类和处理
4. **扩展性好**：后续如需调整异常分类逻辑，只需修改枚举类即可

## 注意事项

1. **PARAM_ERROR响应码保留**：虽然当前逻辑不再返回PARAM_ERROR，但枚举值保留以保持向后兼容
2. **消息安全性**：业务异常消息直接返回给调用方，需确保消息内容不包含敏感信息
3. **日志记录**：所有异常都会在日志中记录完整的堆栈信息，便于问题排查

## 测试建议

建议针对以下场景进行测试：

1. **系统异常测试**：模拟空指针、数组越界等系统级异常
2. **业务异常测试**：抛出带有具体业务消息的异常
3. **边界情况测试**：异常消息为空字符串、只包含空格等情况
4. **加解密异常测试**：验证加解密过程中的异常处理是否正确
