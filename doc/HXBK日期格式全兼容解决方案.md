# HXBK日期格式全兼容解决方案

## 问题描述

在HXBK授信查询接口调用过程中，发现接口返回的日期字段存在多种格式，包括：
- 字符串格式的日期时间
- 数字格式的时间戳
- 不同的日期时间分隔符

导致JSON反序列化异常：
```
com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "2025-07-29T00:00:00": expected format "yyyy-MM-dd HH:mm:ss"
```

## 实际响应数据分析

### 示例1：混合格式
```json
{
  "credit_info": {
    "expire_date_sup": "2026-07-29",
    "pay_date": "2025-07-29T00:00:00",
    "expire_date": "2026-07-29T00:00:00",
    "pay_date_sup": "2025-07-29"
  }
}
```

### 示例2：包含时间戳
```json
{
  "credit_info": {
    "credit_amount": 17800.00,
    "rest_amount": 17800.00,
    "pay_date": "2025-07-29 14:27:40",
    "expire_date": 1785335260517,
    "rate_unit": "1",
    "rate_value": 0.2399,
    "repay_way": "1",
    "status": "0"
  }
}
```

## 需要兼容的格式

1. **时间戳格式**：
   - 毫秒级：`1785335260517`
   - 秒级：`1785335260`
   - 字符串时间戳：`"1785335260517"`

2. **字符串日期格式**：
   - ISO 8601格式：`"2025-07-29T00:00:00"`
   - 标准格式：`"2025-07-29 14:27:40"`
   - 日期格式：`"2025-07-29"`

## 解决方案

### 创建全兼容的日期反序列化器

```java
/**
 * 灵活的Date反序列化器，支持多种日期格式
 * 支持的格式包括：
 * 1. 时间戳格式：1785335260517 (毫秒级) 或 1785335260 (秒级)
 * 2. ISO 8601格式：2025-07-29T00:00:00
 * 3. 标准格式：2025-07-29 00:00:00
 * 4. 日期格式：2025-07-29
 */
public class FlexibleDateDeserializer extends JsonDeserializer<Date> {
    
    // 支持的日期格式（按优先级排序）
    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd'T'HH:mm:ss",    // ISO 8601格式
        "yyyy-MM-dd HH:mm:ss",      // 标准格式
        "yyyy-MM-dd"                // 日期格式
    };
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 1. 首先检查是否为数字类型（时间戳）
        if (p.getCurrentToken().isNumeric()) {
            try {
                long timestamp = p.getValueAsLong();
                // 判断是秒级还是毫秒级时间戳
                if (timestamp <= **********L) {
                    return new Date(timestamp * 1000);  // 秒级转毫秒
                } else {
                    return new Date(timestamp);         // 毫秒级直接使用
                }
            } catch (Exception e) {
                throw new IOException("Failed to parse timestamp: " + p.getValueAsString(), e);
            }
        }
        
        // 2. 处理字符串格式的日期
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 3. 检查是否为纯数字字符串（时间戳）
        if (value.matches("\\d+")) {
            try {
                long timestamp = Long.parseLong(value);
                if (timestamp <= **********L) {
                    return new Date(timestamp * 1000);
                } else {
                    return new Date(timestamp);
                }
            } catch (NumberFormatException e) {
                // 继续尝试日期格式解析
            }
        }
        
        // 4. 尝试各种日期格式进行解析
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
                return sdf.parse(value);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        // 所有格式都失败，抛出异常
        throw new IOException("Failed to parse date: " + value + 
            ". Supported formats: " + String.join(", ", DATE_PATTERNS) + ", timestamp");
    }
}
```

### 应用到DTO类

```java
public class HXBKCreditAmount {
    
    @JsonProperty("pay_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date payDate;

    @JsonProperty("expire_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDate;

    @JsonProperty("expire_date_sup")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDateSup;

    @JsonProperty("pay_date_sup")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date payDateSup;
}
```

## 支持的格式总览

| 类型 | 格式 | 示例 | 说明 |
|------|------|------|------|
| 数字时间戳 | 毫秒级 | `1785335260517` | 13位数字，毫秒级时间戳 |
| 数字时间戳 | 秒级 | `1785335260` | 10位数字，秒级时间戳 |
| 字符串时间戳 | 毫秒级 | `"1785335260517"` | 字符串形式的毫秒时间戳 |
| 字符串时间戳 | 秒级 | `"1785335260"` | 字符串形式的秒级时间戳 |
| ISO 8601 | 带T分隔符 | `"2025-07-29T00:00:00"` | 标准ISO格式 |
| 标准格式 | 空格分隔符 | `"2025-07-29 14:27:40"` | 常用日期时间格式 |
| 日期格式 | 仅日期 | `"2025-07-29"` | 纯日期格式 |

## 解析优先级

1. **数字类型检查**：优先检查JSON中的数字类型
2. **字符串时间戳**：检查纯数字字符串
3. **日期格式解析**：按优先级尝试各种日期格式

## 时间戳判断逻辑

- **≤ ************ (10位数字)：认为是秒级时间戳，乘以1000转换为毫秒
- **> ************ (13位数字)：认为是毫秒级时间戳，直接使用

## 优势

- ✅ **全格式兼容**：支持所有已知的HXBK日期格式
- ✅ **智能识别**：自动识别时间戳类型和日期格式
- ✅ **性能优化**：按使用频率排序，常用格式优先
- ✅ **错误处理**：详细的错误信息，便于调试
- ✅ **时区统一**：所有日期统一使用GMT+8时区

## 相关文件

- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/config/FlexibleDateDeserializer.java`
- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/credit/HXBKCreditAmount.java`

## 修复时间

2025-07-29
