# HXBK日期格式兼容性问题修复

## 问题描述

在HXBK授信查询接口调用过程中，出现JSON反序列化异常：

```
com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "2025-07-29T00:00:00": expected format "yyyy-MM-dd HH:mm:ss"
```

## 问题分析

### 错误原因
HXBK接口可能返回多种日期格式，需要同时兼容：
1. **ISO 8601格式**：`yyyy-MM-dd'T'HH:mm:ss` (如：`2025-07-29T00:00:00`)
2. **标准格式**：`yyyy-MM-dd HH:mm:ss` (如：`2025-07-29 00:00:00`)
3. **日期格式**：`yyyy-MM-dd` (如：`2025-07-29`)

### 响应数据示例
```json
{
  "credit_info": {
    "expire_date_sup": "2026-07-29",
    "pay_date": "2025-07-29T00:00:00",
    "expire_date": "2026-07-29T00:00:00",
    "pay_date_sup": "2025-07-29"
  }
}
```

## 解决方案

### 方案：灵活反序列化器（推荐）
创建 `FlexibleDateDeserializer` 类，支持多种日期格式的自动识别和解析。

#### 1. 创建灵活的日期反序列化器

```java
public class FlexibleDateDeserializer extends JsonDeserializer<Date> {
    
    // 支持的日期格式（按优先级排序）
    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd'T'HH:mm:ss",    // ISO 8601格式：2025-07-29T00:00:00
        "yyyy-MM-dd HH:mm:ss",      // 标准格式：2025-07-29 00:00:00
        "yyyy-MM-dd"                // 日期格式：2025-07-29
    };
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 尝试各种格式进行解析
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
                return sdf.parse(value);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        // 所有格式都失败，抛出异常
        throw new IOException("Failed to parse date: " + value + 
            ". Supported formats: " + String.join(", ", DATE_PATTERNS));
    }
}
```

#### 2. 修改DTO类使用灵活反序列化器

```java
// 导入必要的类
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleDateDeserializer;

public class HXBKCreditAmount {
    
    @JsonProperty("pay_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date payDate;

    @JsonProperty("expire_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDate;

    @JsonProperty("expire_date_sup")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private Date expireDateSup;
    
    // pay_date_sup 保持原有的 @JsonFormat 注解
    @JsonProperty("pay_date_sup")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date payDateSup;
}
```

## 修改内容

1. **创建FlexibleDateDeserializer类**：支持多种日期格式的自动识别
2. **pay_date字段**：使用 `@JsonDeserialize(using = FlexibleDateDeserializer.class)`
3. **expire_date字段**：使用 `@JsonDeserialize(using = FlexibleDateDeserializer.class)`
4. **expire_date_sup字段**：使用 `@JsonDeserialize(using = FlexibleDateDeserializer.class)`
5. **pay_date_sup字段**：保持原有的 `@JsonFormat` 注解

## 优势

1. **兼容性强**：同时支持带T和不带T的日期格式
2. **扩展性好**：可以轻松添加新的日期格式支持
3. **错误处理**：提供详细的错误信息，便于调试
4. **时区支持**：统一使用GMT+8时区
5. **性能优化**：按优先级尝试格式，常用格式优先

## 支持的格式

| 格式 | 示例 | 说明 |
|------|------|------|
| `yyyy-MM-dd'T'HH:mm:ss` | `2025-07-29T00:00:00` | ISO 8601格式 |
| `yyyy-MM-dd HH:mm:ss` | `2025-07-29 00:00:00` | 标准日期时间格式 |
| `yyyy-MM-dd` | `2025-07-29` | 纯日期格式 |

## 验证

修改后的代码能够正确解析HXBK返回的所有日期格式，不再出现JSON反序列化异常。

## 相关文件

- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/config/FlexibleDateDeserializer.java`
- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/credit/HXBKCreditAmount.java`

## 修复时间

2025-07-29
